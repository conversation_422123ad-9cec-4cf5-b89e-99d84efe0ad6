<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'My Profile';
$teacher_id = $_SESSION['user_id'];
$success_message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $full_name = trim($_POST['full_name']);
        $email = trim($_POST['email']);
        $phone = trim($_POST['phone']);
        $address = trim($_POST['address']);
        $office_hours = trim($_POST['office_hours']);
        $bio = trim($_POST['bio']);

        // Validate required fields
        if (empty($full_name) || empty($email)) {
            throw new Exception("Full name and email are required.");
        }

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Please enter a valid email address.");
        }

        // Check if email is already taken by another user
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $stmt->execute([$email, $teacher_id]);
        if ($stmt->fetch()) {
            throw new Exception("This email address is already in use by another user.");
        }

        // Update user profile
        $stmt = $db->prepare("
            UPDATE users
            SET full_name = ?, email = ?, phone = ?, address = ?, updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$full_name, $email, $phone, $address, $teacher_id]);

        // Update session data
        $_SESSION['full_name'] = $full_name;
        $_SESSION['email'] = $email;

        $success_message = "Profile updated successfully!";

    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Initialize variables
$teacher = null;
$departments = [];
$recent_sessions = [];

// Get teacher profile data
try {
    // First, check which enrollment table exists
    $enrollment_table = 'enrollments';
    try {
        $db->query("SELECT 1 FROM class_enrollments LIMIT 1");
        $enrollment_table = 'class_enrollments';
    } catch (PDOException $e) {
        // class_enrollments doesn't exist, use enrollments
    }

    $stmt = $db->prepare("
        SELECT
            u.*,
            COUNT(DISTINCT c.id) as total_classes,
            COUNT(DISTINCT ce.student_id) as total_students,
            COUNT(DISTINCT ats.id) as total_sessions
        FROM users u
        LEFT JOIN classes c ON u.id = c.teacher_id
        LEFT JOIN $enrollment_table ce ON c.id = ce.class_id
        LEFT JOIN attendance_sessions ats ON c.id = ats.class_id
        WHERE u.id = ?
        GROUP BY u.id
    ");
    $stmt->execute([$teacher_id]);
    $teacher = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$teacher) {
        throw new Exception("Teacher profile not found.");
    }

    // Get teacher's departments
    $stmt = $db->prepare("
        SELECT DISTINCT d.name as department_name, d.code as department_code
        FROM classes c
        JOIN courses co ON c.course_id = co.id
        JOIN departments d ON co.department_id = d.id
        WHERE c.teacher_id = ?
    ");
    $stmt->execute([$teacher_id]);
    $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get recent activity
    $stmt = $db->prepare("
        SELECT
            ats.date,
            ats.start_time,
            ats.end_time,
            co.name as course_name,
            co.code as course_code,
            c.section,
            COUNT(ar.id) as attendance_records
        FROM attendance_sessions ats
        JOIN classes c ON ats.class_id = c.id
        JOIN courses co ON c.course_id = co.id
        LEFT JOIN attendance_records ar ON ats.id = ar.session_id
        WHERE c.teacher_id = ?
        GROUP BY ats.id, ats.date, ats.start_time, ats.end_time, co.name, co.code, c.section
        ORDER BY ats.date DESC, ats.start_time DESC
        LIMIT 5
    ");
    $stmt->execute([$teacher_id]);
    $recent_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    $error_message = $e->getMessage();
}

// If teacher data couldn't be loaded, redirect to login
if (!$teacher) {
    header('Location: ../auth/login.php');
    exit();
}

include '../includes/header.php';
?>

<div class="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-user-cog mr-3 text-indigo-600"></i>
                    My Profile
                </h1>
                <p class="mt-2 text-gray-600">Manage your personal information and account settings</p>
            </div>
            <a href="dashboard.php" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success_message): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <i class="fas fa-check-circle mr-2"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <i class="fas fa-exclamation-circle mr-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Summary -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Basic Info Card -->
            <div class="bg-white shadow-lg rounded-lg p-6">
                <div class="text-center">
                    <div class="h-20 w-20 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-chalkboard-teacher text-indigo-600 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($teacher['full_name']); ?></h3>
                    <p class="text-sm text-gray-600 mb-2"><?php echo htmlspecialchars($teacher['email']); ?></p>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                        Teacher
                    </span>
                </div>

                <div class="mt-6 space-y-3">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Classes:</span>
                        <span class="font-medium text-gray-900"><?php echo $teacher['total_classes'] ?? 0; ?></span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Students:</span>
                        <span class="font-medium text-gray-900"><?php echo $teacher['total_students'] ?? 0; ?></span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Sessions:</span>
                        <span class="font-medium text-gray-900"><?php echo $teacher['total_sessions'] ?? 0; ?></span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Member Since:</span>
                        <span class="font-medium text-gray-900"><?php echo date('M Y', strtotime($teacher['created_at'])); ?></span>
                    </div>
                </div>
            </div>

            <!-- Departments Card -->
            <?php if (!empty($departments)): ?>
                <div class="bg-white shadow-lg rounded-lg p-6">
                    <h4 class="text-md font-semibold text-gray-900 mb-4">
                        <i class="fas fa-building mr-2 text-indigo-600"></i>
                        Departments
                    </h4>
                    <div class="space-y-2">
                        <?php foreach ($departments as $dept): ?>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-900"><?php echo htmlspecialchars($dept['department_name']); ?></span>
                                <span class="text-gray-500"><?php echo htmlspecialchars($dept['department_code']); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Recent Activity -->
            <?php if (!empty($recent_sessions)): ?>
                <div class="bg-white shadow-lg rounded-lg p-6">
                    <h4 class="text-md font-semibold text-gray-900 mb-4">
                        <i class="fas fa-history mr-2 text-indigo-600"></i>
                        Recent Sessions
                    </h4>
                    <div class="space-y-3">
                        <?php foreach ($recent_sessions as $session): ?>
                            <div class="border-l-4 border-indigo-400 pl-3">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($session['course_name']); ?></div>
                                <div class="text-xs text-gray-500">
                                    <?php echo date('M d, Y', strtotime($session['date'])); ?> •
                                    <?php echo $session['attendance_records']; ?> records
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Profile Form -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-edit mr-2 text-indigo-600"></i>
                        Edit Profile Information
                    </h3>
                </div>

                <form method="POST" class="p-6 space-y-6">
                    <!-- Personal Information -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-4">Personal Information</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="full_name" class="block text-sm font-medium text-gray-700 mb-1">
                                    Full Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="full_name" name="full_name" required
                                       value="<?php echo htmlspecialchars($teacher['full_name']); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                                    Email Address <span class="text-red-500">*</span>
                                </label>
                                <input type="email" id="email" name="email" required
                                       value="<?php echo htmlspecialchars($teacher['email']); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>

                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">
                                    Phone Number
                                </label>
                                <input type="tel" id="phone" name="phone"
                                       value="<?php echo htmlspecialchars($teacher['phone'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>

                            <div>
                                <label for="office_hours" class="block text-sm font-medium text-gray-700 mb-1">
                                    Office Hours
                                </label>
                                <input type="text" id="office_hours" name="office_hours"
                                       value="<?php echo htmlspecialchars($teacher['office_hours'] ?? ''); ?>"
                                       placeholder="e.g., Mon-Fri 2:00-4:00 PM"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>
                        </div>
                    </div>

                    <!-- Address -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-4">Contact Information</h4>
                        <div>
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-1">
                                Address
                            </label>
                            <textarea id="address" name="address" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                      placeholder="Enter your address"><?php echo htmlspecialchars($teacher['address'] ?? ''); ?></textarea>
                        </div>
                    </div>

                    <!-- Bio -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-4">Professional Information</h4>
                        <div>
                            <label for="bio" class="block text-sm font-medium text-gray-700 mb-1">
                                Bio / About Me
                            </label>
                            <textarea id="bio" name="bio" rows="4"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                      placeholder="Tell students about yourself, your teaching philosophy, qualifications, etc."><?php echo htmlspecialchars($teacher['bio'] ?? ''); ?></textarea>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit"
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-save mr-2"></i>
                            Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>

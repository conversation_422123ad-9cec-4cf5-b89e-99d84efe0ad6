<?php
session_start();
require_once '../config/database.php';

// Check if user is teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Teacher Dashboard';
$teacher_id = $_SESSION['user_id'];

// Get teacher's statistics
try {
    // First, check which enrollment table exists
    $enrollment_table = 'enrollments';
    try {
        $db->query("SELECT 1 FROM class_enrollments LIMIT 1");
        $enrollment_table = 'class_enrollments';
    } catch (PDOException $e) {
        // class_enrollments doesn't exist, use enrollments
    }

    // Get teacher's classes
    $stmt = $db->prepare("
        SELECT
            cl.id,
            cl.section,
            cl.academic_year,
            c.name as course_name,
            c.code as course_code,
            d.name as department_name,
            COUNT(e.student_id) as total_students
        FROM classes cl
        JOIN courses c ON cl.course_id = c.id
        JOIN departments d ON c.department_id = d.id
        LEFT JOIN $enrollment_table e ON cl.id = e.class_id AND e.status = 'active'
        WHERE cl.teacher_id = :teacher_id
        GROUP BY cl.id
        ORDER BY c.name, cl.section
    ");
    $stmt->bindParam(':teacher_id', $teacher_id);
    $stmt->execute();
    $teacher_classes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get total students across all classes
    $stmt = $db->prepare("
        SELECT COUNT(DISTINCT e.student_id) as total_students
        FROM classes cl
        JOIN $enrollment_table e ON cl.id = e.class_id AND e.status = 'active'
        WHERE cl.teacher_id = :teacher_id
    ");
    $stmt->bindParam(':teacher_id', $teacher_id);
    $stmt->execute();
    $total_students = $stmt->fetchColumn();

    // Get recent attendance sessions
    $stmt = $db->prepare("
        SELECT
            ats.id,
            ats.date,
            ats.start_time,
            ats.end_time,
            ats.topic,
            c.name as course_name,
            cl.section,
            COUNT(ar.id) as total_marked,
            SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) as present_count
        FROM attendance_sessions ats
        JOIN classes cl ON ats.class_id = cl.id
        JOIN courses c ON cl.course_id = c.id
        LEFT JOIN attendance_records ar ON ats.id = ar.session_id
        WHERE cl.teacher_id = :teacher_id
        GROUP BY ats.id
        ORDER BY ats.date DESC, ats.start_time DESC
        LIMIT 5
    ");
    $stmt->bindParam(':teacher_id', $teacher_id);
    $stmt->execute();
    $recent_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get today's classes
    $today = date('Y-m-d');
    $stmt = $db->prepare("
        SELECT
            cl.id,
            cl.section,
            c.name as course_name,
            c.code as course_code,
            COUNT(e.student_id) as total_students
        FROM classes cl
        JOIN courses c ON cl.course_id = c.id
        LEFT JOIN $enrollment_table e ON cl.id = e.class_id AND e.status = 'active'
        WHERE cl.teacher_id = :teacher_id
        GROUP BY cl.id
        ORDER BY c.name
    ");
    $stmt->bindParam(':teacher_id', $teacher_id);
    $stmt->execute();
    $today_classes = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
    // Initialize variables with empty arrays to prevent count() errors
    $teacher_classes = [];
    $recent_sessions = [];
    $total_students = 0;
    $today_classes = [];
}

include '../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Teacher Dashboard</h1>
        <p class="mt-2 text-gray-600">Welcome back, <?php echo htmlspecialchars($_SESSION['full_name']); ?>!</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Total Classes -->
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-chalkboard text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">My Classes</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo count($teacher_classes); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Students -->
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-user-graduate text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Students</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_students; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sessions Conducted -->
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-calendar-check text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Sessions Conducted</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo count($recent_sessions); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Attendance Management -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-clipboard-check mr-2 text-indigo-600"></i>
                Attendance Management
            </h3>
            <div class="space-y-3">
                <a href="mark-attendance.php" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition duration-200">
                    <i class="fas fa-plus-circle text-green-600 mr-3"></i>
                    <div>
                        <span class="text-sm font-medium text-green-900 block">Mark Attendance</span>
                        <span class="text-xs text-green-700">Create new attendance session</span>
                    </div>
                </a>
                <a href="view-attendance.php" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition duration-200">
                    <i class="fas fa-eye text-blue-600 mr-3"></i>
                    <div>
                        <span class="text-sm font-medium text-blue-900 block">View Attendance</span>
                        <span class="text-xs text-blue-700">Check attendance records</span>
                    </div>
                </a>
                <a href="attendance-reports.php" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition duration-200">
                    <i class="fas fa-chart-bar text-purple-600 mr-3"></i>
                    <div>
                        <span class="text-sm font-medium text-purple-900 block">Attendance Reports</span>
                        <span class="text-xs text-purple-700">Generate detailed reports</span>
                    </div>
                </a>
            </div>
        </div>

        <!-- Class Management -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-users mr-2 text-indigo-600"></i>
                Class Management
            </h3>
            <div class="space-y-3">
                <a href="my-classes.php" class="flex items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition duration-200">
                    <i class="fas fa-chalkboard text-orange-600 mr-3"></i>
                    <div>
                        <span class="text-sm font-medium text-orange-900 block">My Classes</span>
                        <span class="text-xs text-orange-700">View all assigned classes</span>
                    </div>
                </a>
                <a href="students.php" class="flex items-center p-4 bg-red-50 rounded-lg hover:bg-red-100 transition duration-200">
                    <i class="fas fa-user-graduate text-red-600 mr-3"></i>
                    <div>
                        <span class="text-sm font-medium text-red-900 block">Student List</span>
                        <span class="text-xs text-red-700">View enrolled students</span>
                    </div>
                </a>
                <a href="profile.php" class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-200">
                    <i class="fas fa-user-cog text-gray-600 mr-3"></i>
                    <div>
                        <span class="text-sm font-medium text-gray-900 block">My Profile</span>
                        <span class="text-xs text-gray-700">Update profile information</span>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- My Classes -->
    <div class="bg-white shadow-lg rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-chalkboard mr-2 text-indigo-600"></i>
                My Classes
            </h3>
        </div>
        <div class="p-6">
            <?php if (!empty($teacher_classes)): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php foreach ($teacher_classes as $class): ?>
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition duration-200">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h4 class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($class['course_name']); ?></h4>
                                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($class['course_code']); ?> - Section <?php echo htmlspecialchars($class['section']); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo htmlspecialchars($class['department_name']); ?></p>
                                    <p class="text-sm text-indigo-600 mt-2">
                                        <i class="fas fa-users mr-1"></i>
                                        <?php echo $class['total_students']; ?> Students
                                    </p>
                                </div>
                            </div>
                            <div class="mt-4 flex space-x-2">
                                <a href="mark-attendance.php?class_id=<?php echo $class['id']; ?>" class="flex-1 bg-green-600 text-white text-xs px-3 py-2 rounded text-center hover:bg-green-700 transition duration-200">
                                    Mark Attendance
                                </a>
                                <a href="view-attendance.php?class_id=<?php echo $class['id']; ?>" class="flex-1 bg-blue-600 text-white text-xs px-3 py-2 rounded text-center hover:bg-blue-700 transition duration-200">
                                    View Records
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <i class="fas fa-chalkboard text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500">No classes assigned yet.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recent Attendance Sessions -->
    <div class="bg-white shadow-lg rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-clock mr-2 text-indigo-600"></i>
                Recent Attendance Sessions
            </h3>
        </div>
        <div class="p-6">
            <?php if (!empty($recent_sessions)): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Topic</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($recent_sessions as $session): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('M d, Y', strtotime($session['date'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('h:i A', strtotime($session['start_time'])) . ' - ' . date('h:i A', strtotime($session['end_time'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($session['course_name']); ?> - <?php echo htmlspecialchars($session['section']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($session['topic'] ?: 'N/A'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $session['present_count']; ?>/<?php echo $session['total_marked']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="view-session.php?id=<?php echo $session['id']; ?>" class="text-indigo-600 hover:text-indigo-900">View Details</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500">No attendance sessions found.</p>
                    <a href="mark-attendance.php" class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                        <i class="fas fa-plus mr-2"></i>
                        Create First Session
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>

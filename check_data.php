<?php
require_once 'config/database.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Database Data Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Database Data Check</h1>";

try {
    // Check class_enrollments table
    echo "<h2>1. Class Enrollments</h2>";
    $stmt = $db->query("SELECT COUNT(*) as count FROM enrollments");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<div class='info'>Total enrollments: $count</div>";
    
    if ($count > 0) {
        $stmt = $db->query("
            SELECT 
                ce.id,
                ce.student_id,
                ce.class_id,
                ce.status,
                u.full_name as student_name,
                c.name as course_name,
                cl.section
            FROM enrollments ce
            JOIN users u ON ce.student_id = u.id
            JOIN classes cl ON ce.class_id = cl.id
            JOIN courses c ON cl.course_id = c.id
            LIMIT 10
        ");
        $enrollments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Student</th><th>Course</th><th>Section</th><th>Status</th></tr>";
        foreach ($enrollments as $enrollment) {
            echo "<tr>";
            echo "<td>{$enrollment['id']}</td>";
            echo "<td>{$enrollment['student_name']}</td>";
            echo "<td>{$enrollment['course_name']}</td>";
            echo "<td>{$enrollment['section']}</td>";
            echo "<td>{$enrollment['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>No enrollment records found!</div>";
    }
    
    // Check classes table
    echo "<h2>2. Classes</h2>";
    $stmt = $db->query("SELECT COUNT(*) as count FROM classes");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<div class='info'>Total classes: $count</div>";
    
    if ($count > 0) {
        $stmt = $db->query("
            SELECT 
                cl.id,
                cl.section,
                c.name as course_name,
                c.code as course_code,
                u.full_name as teacher_name
            FROM classes cl
            JOIN courses c ON cl.course_id = c.id
            JOIN users u ON cl.teacher_id = u.id
            LIMIT 10
        ");
        $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Course</th><th>Code</th><th>Section</th><th>Teacher</th></tr>";
        foreach ($classes as $class) {
            echo "<tr>";
            echo "<td>{$class['id']}</td>";
            echo "<td>{$class['course_name']}</td>";
            echo "<td>{$class['course_code']}</td>";
            echo "<td>{$class['section']}</td>";
            echo "<td>{$class['teacher_name']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check attendance_sessions table
    echo "<h2>3. Attendance Sessions</h2>";
    $stmt = $db->query("SELECT COUNT(*) as count FROM attendance_sessions");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<div class='info'>Total attendance sessions: $count</div>";
    
    if ($count > 0) {
        $stmt = $db->query("
            SELECT 
                ats.id,
                ats.date,
                ats.start_time,
                ats.end_time,
                ats.topic,
                c.name as course_name,
                cl.section
            FROM attendance_sessions ats
            JOIN classes cl ON ats.class_id = cl.id
            JOIN courses c ON cl.course_id = c.id
            ORDER BY ats.date DESC
            LIMIT 10
        ");
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Date</th><th>Time</th><th>Course</th><th>Section</th><th>Topic</th></tr>";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>{$session['id']}</td>";
            echo "<td>{$session['date']}</td>";
            echo "<td>{$session['start_time']} - {$session['end_time']}</td>";
            echo "<td>{$session['course_name']}</td>";
            echo "<td>{$session['section']}</td>";
            echo "<td>{$session['topic']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>No attendance sessions found!</div>";
    }
    
    // Check attendance_records table
    echo "<h2>4. Attendance Records</h2>";
    $stmt = $db->query("SELECT COUNT(*) as count FROM attendance_records");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<div class='info'>Total attendance records: $count</div>";
    
    if ($count > 0) {
        $stmt = $db->query("
            SELECT 
                ar.id,
                ar.status,
                u.full_name as student_name,
                ats.date,
                c.name as course_name
            FROM attendance_records ar
            JOIN users u ON ar.student_id = u.id
            JOIN attendance_sessions ats ON ar.session_id = ats.id
            JOIN classes cl ON ats.class_id = cl.id
            JOIN courses c ON cl.course_id = c.id
            ORDER BY ats.date DESC
            LIMIT 10
        ");
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Student</th><th>Course</th><th>Date</th><th>Status</th></tr>";
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>{$record['id']}</td>";
            echo "<td>{$record['student_name']}</td>";
            echo "<td>{$record['course_name']}</td>";
            echo "<td>{$record['date']}</td>";
            echo "<td>{$record['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='warning'>No attendance records found!</div>";
    }
    
    // Check users table
    echo "<h2>5. Users</h2>";
    $stmt = $db->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>Role</th><th>Count</th></tr>";
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>{$user['role']}</td>";
        echo "<td>{$user['count']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>Summary</h2>";
    echo "<div class='info'>If you see 0 records for enrollments, attendance sessions, or attendance records, that's why the dashboard shows no class-wise attendance data.</div>";
    echo "<div class='info'>You need to:</div>";
    echo "<ul>";
    echo "<li>1. Create some sample enrollment data</li>";
    echo "<li>2. Create some attendance sessions</li>";
    echo "<li>3. Mark attendance for students</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<div class='error'>Database error: " . $e->getMessage() . "</div>";
}

echo "</body></html>";
?>

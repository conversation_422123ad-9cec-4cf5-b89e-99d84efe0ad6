-- Fix for class_enrollments table issue
-- This script will create the class_enrollments table that the application expects

USE attendance_system;

-- Drop the existing enrollments table if it exists
DROP TABLE IF EXISTS enrollments;

-- Create the class_enrollments table with the correct structure
CREATE TABLE class_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    class_id INT NOT NULL,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'dropped', 'completed') DEFAULT 'active',
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_enrollment (student_id, class_id)
);

-- Add missing columns to classes table that the application expects
ALTER TABLE classes 
ADD COLUMN IF NOT EXISTS schedule_day VARCHAR(20) AFTER schedule,
ADD COLUMN IF NOT EXISTS start_time TIME AFTER schedule_day,
ADD COLUMN IF NOT EXISTS end_time TIME AFTER start_time,
ADD COLUMN IF NOT EXISTS room_number VARCHAR(50) AFTER end_time;

-- Update the existing schedule column data to populate the new columns
-- This is a basic migration - you may need to adjust based on your existing data format
UPDATE classes 
SET 
    schedule_day = 'Monday',
    start_time = '09:00:00',
    end_time = '10:30:00',
    room_number = room
WHERE schedule_day IS NULL;

-- Insert some sample enrollments for testing (optional)
-- You can remove this section if you don't want sample data
INSERT IGNORE INTO class_enrollments (student_id, class_id, enrolled_at, status) 
SELECT 
    u.id as student_id,
    c.id as class_id,
    NOW() as enrolled_at,
    'active' as status
FROM users u
CROSS JOIN classes c
WHERE u.role = 'student'
LIMIT 10;

-- Display success message
SELECT 'class_enrollments table created successfully!' as message;
SELECT 'Database structure has been fixed.' as info;

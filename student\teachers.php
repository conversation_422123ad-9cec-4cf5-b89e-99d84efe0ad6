<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'My Teachers';
$student_id = $_SESSION['user_id'];

// Get student's teachers
try {
    $stmt = $db->prepare("
        SELECT DISTINCT
            u.id as teacher_id,
            u.full_name as teacher_name,
            u.email as teacher_email,
            u.phone,
            d.name as department_name,
            d.code as department_code,
            GROUP_CONCAT(
                CONCAT(co.name, ' (', co.code, ') - Section ', c.section)
                ORDER BY co.name SEPARATOR '; '
            ) as courses_taught
        FROM class_enrollments ce
        JOIN classes c ON ce.class_id = c.id
        JOIN courses co ON c.course_id = co.id
        JOIN users u ON c.teacher_id = u.id
        JOIN departments d ON co.department_id = d.id
        WHERE ce.student_id = ? AND u.role = 'teacher'
        GROUP BY u.id, u.full_name, u.email, u.phone, d.name, d.code
        ORDER BY u.full_name
    ");
    $stmt->execute([$student_id]);
    $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get class schedule for each teacher
    $teacher_schedules = [];
    foreach ($teachers as $teacher) {
        $stmt = $db->prepare("
            SELECT 
                c.schedule_day,
                c.start_time,
                c.end_time,
                c.room_number,
                c.section,
                co.name as course_name,
                co.code as course_code
            FROM class_enrollments ce
            JOIN classes c ON ce.class_id = c.id
            JOIN courses co ON c.course_id = co.id
            WHERE ce.student_id = ? AND c.teacher_id = ?
            ORDER BY 
                CASE c.schedule_day 
                    WHEN 'Monday' THEN 1
                    WHEN 'Tuesday' THEN 2
                    WHEN 'Wednesday' THEN 3
                    WHEN 'Thursday' THEN 4
                    WHEN 'Friday' THEN 5
                    WHEN 'Saturday' THEN 6
                    WHEN 'Sunday' THEN 7
                END,
                c.start_time
        ");
        $stmt->execute([$student_id, $teacher['teacher_id']]);
        $teacher_schedules[$teacher['teacher_id']] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-chalkboard-teacher mr-3 text-indigo-600"></i>
                    My Teachers
                </h1>
                <p class="mt-2 text-gray-600">View information about your course instructors</p>
            </div>
            <a href="dashboard.php" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Teachers List -->
    <?php if (!empty($teachers)): ?>
        <div class="space-y-6">
            <?php foreach ($teachers as $teacher): ?>
                <div class="bg-white shadow-lg rounded-lg overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="h-12 w-12 bg-indigo-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-user text-indigo-600 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($teacher['teacher_name']); ?></h3>
                                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($teacher['department_name']); ?></p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <a href="mailto:<?php echo htmlspecialchars($teacher['teacher_email']); ?>" 
                                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <i class="fas fa-envelope mr-2"></i>
                                    Email
                                </a>
                                <?php if (!empty($teacher['phone'])): ?>
                                    <a href="tel:<?php echo htmlspecialchars($teacher['phone']); ?>" 
                                       class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        <i class="fas fa-phone mr-2"></i>
                                        Call
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Contact Information -->
                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-3">Contact Information</h4>
                                <div class="space-y-2 text-sm text-gray-600">
                                    <div class="flex items-center">
                                        <i class="fas fa-envelope w-4 mr-3 text-gray-400"></i>
                                        <span class="font-medium">Email:</span>
                                        <a href="mailto:<?php echo htmlspecialchars($teacher['teacher_email']); ?>" class="ml-2 text-indigo-600 hover:text-indigo-800">
                                            <?php echo htmlspecialchars($teacher['teacher_email']); ?>
                                        </a>
                                    </div>
                                    <?php if (!empty($teacher['phone'])): ?>
                                        <div class="flex items-center">
                                            <i class="fas fa-phone w-4 mr-3 text-gray-400"></i>
                                            <span class="font-medium">Phone:</span>
                                            <a href="tel:<?php echo htmlspecialchars($teacher['phone']); ?>" class="ml-2 text-indigo-600 hover:text-indigo-800">
                                                <?php echo htmlspecialchars($teacher['phone']); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    <div class="flex items-center">
                                        <i class="fas fa-building w-4 mr-3 text-gray-400"></i>
                                        <span class="font-medium">Department:</span>
                                        <span class="ml-2"><?php echo htmlspecialchars($teacher['department_name']); ?> (<?php echo htmlspecialchars($teacher['department_code']); ?>)</span>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <h5 class="text-sm font-semibold text-gray-900 mb-2">Courses Teaching You</h5>
                                    <div class="text-sm text-gray-600">
                                        <?php 
                                        $courses = explode('; ', $teacher['courses_taught']);
                                        foreach ($courses as $course): 
                                        ?>
                                            <div class="mb-1">• <?php echo htmlspecialchars($course); ?></div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Class Schedule -->
                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-3">Class Schedule with You</h4>
                                <?php if (!empty($teacher_schedules[$teacher['teacher_id']])): ?>
                                    <div class="space-y-3">
                                        <?php foreach ($teacher_schedules[$teacher['teacher_id']] as $schedule): ?>
                                            <div class="border border-gray-200 rounded-lg p-3">
                                                <div class="flex items-center justify-between mb-2">
                                                    <h6 class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($schedule['course_name']); ?></h6>
                                                    <span class="text-xs text-gray-500"><?php echo htmlspecialchars($schedule['course_code']); ?></span>
                                                </div>
                                                <div class="space-y-1 text-xs text-gray-600">
                                                    <div class="flex items-center">
                                                        <i class="fas fa-calendar w-3 mr-2"></i>
                                                        <?php echo htmlspecialchars($schedule['schedule_day']); ?>
                                                    </div>
                                                    <div class="flex items-center">
                                                        <i class="fas fa-clock w-3 mr-2"></i>
                                                        <?php echo date('g:i A', strtotime($schedule['start_time'])) . ' - ' . date('g:i A', strtotime($schedule['end_time'])); ?>
                                                    </div>
                                                    <div class="flex items-center">
                                                        <i class="fas fa-map-marker-alt w-3 mr-2"></i>
                                                        Room <?php echo htmlspecialchars($schedule['room_number'] ?: 'TBA'); ?>
                                                    </div>
                                                    <div class="flex items-center">
                                                        <i class="fas fa-users w-3 mr-2"></i>
                                                        Section <?php echo htmlspecialchars($schedule['section']); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="text-sm text-gray-500">No scheduled classes found.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="bg-white shadow-lg rounded-lg">
            <div class="text-center py-12">
                <i class="fas fa-chalkboard-teacher text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Teachers Found</h3>
                <p class="text-gray-500">You don't have any assigned teachers yet. Please contact your academic advisor for assistance.</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>

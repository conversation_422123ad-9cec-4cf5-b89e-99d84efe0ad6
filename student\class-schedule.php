<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Class Schedule';
$student_id = $_SESSION['user_id'];

// Get student's class schedule
try {
    // Get student's enrolled classes with schedule information
    $stmt = $db->prepare("
        SELECT 
            c.id as class_id,
            c.section,
            c.schedule_day,
            c.start_time,
            c.end_time,
            c.room_number,
            co.name as course_name,
            co.code as course_code,
            co.credits,
            u.full_name as teacher_name,
            u.email as teacher_email,
            d.name as department_name
        FROM class_enrollments ce
        JOIN classes c ON ce.class_id = c.id
        JOIN courses co ON c.course_id = co.id
        JOIN users u ON c.teacher_id = u.id
        JOIN departments d ON co.department_id = d.id
        WHERE ce.student_id = ?
        ORDER BY 
            CASE c.schedule_day 
                WHEN 'Monday' THEN 1
                WHEN 'Tuesday' THEN 2
                WHEN 'Wednesday' THEN 3
                WHEN 'Thursday' THEN 4
                WHEN 'Friday' THEN 5
                WHEN 'Saturday' THEN 6
                WHEN 'Sunday' THEN 7
            END,
            c.start_time
    ");
    $stmt->execute([$student_id]);
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Group classes by day
    $schedule_by_day = [];
    foreach ($classes as $class) {
        $day = $class['schedule_day'];
        if (!isset($schedule_by_day[$day])) {
            $schedule_by_day[$day] = [];
        }
        $schedule_by_day[$day][] = $class;
    }

    // Get upcoming classes (next 7 days)
    $stmt = $db->prepare("
        SELECT 
            ats.id as session_id,
            ats.date,
            ats.start_time,
            ats.end_time,
            c.section,
            c.room_number,
            co.name as course_name,
            co.code as course_code,
            u.full_name as teacher_name
        FROM attendance_sessions ats
        JOIN classes c ON ats.class_id = c.id
        JOIN courses co ON c.course_id = co.id
        JOIN users u ON c.teacher_id = u.id
        JOIN class_enrollments ce ON c.id = ce.class_id
        WHERE ce.student_id = ? 
        AND ats.date >= CURDATE() 
        AND ats.date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)
        ORDER BY ats.date, ats.start_time
        LIMIT 10
    ");
    $stmt->execute([$student_id]);
    $upcoming_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-calendar mr-3 text-indigo-600"></i>
                    Class Schedule
                </h1>
                <p class="mt-2 text-gray-600">View your weekly class schedule and upcoming sessions</p>
            </div>
            <a href="dashboard.php" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Upcoming Classes -->
    <?php if (!empty($upcoming_sessions)): ?>
        <div class="bg-white shadow-lg rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-clock mr-2 text-indigo-600"></i>
                    Upcoming Classes (Next 7 Days)
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php foreach ($upcoming_sessions as $session): ?>
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition duration-200">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($session['course_name']); ?></h4>
                                    <p class="text-xs text-gray-500 mb-2"><?php echo htmlspecialchars($session['course_code']); ?> - Section <?php echo htmlspecialchars($session['section']); ?></p>
                                    
                                    <div class="space-y-1 text-xs text-gray-600">
                                        <div class="flex items-center">
                                            <i class="fas fa-calendar-day w-4 mr-2"></i>
                                            <?php echo date('M d, Y (l)', strtotime($session['date'])); ?>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-clock w-4 mr-2"></i>
                                            <?php echo date('g:i A', strtotime($session['start_time'])) . ' - ' . date('g:i A', strtotime($session['end_time'])); ?>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-map-marker-alt w-4 mr-2"></i>
                                            <?php echo htmlspecialchars($session['room_number'] ?: 'TBA'); ?>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-user w-4 mr-2"></i>
                                            <?php echo htmlspecialchars($session['teacher_name']); ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="ml-2">
                                    <?php
                                    $days_until = (strtotime($session['date']) - strtotime(date('Y-m-d'))) / (60 * 60 * 24);
                                    if ($days_until == 0) {
                                        echo '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Today</span>';
                                    } elseif ($days_until == 1) {
                                        echo '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Tomorrow</span>';
                                    } else {
                                        echo '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">' . $days_until . ' days</span>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Weekly Schedule -->
    <div class="bg-white shadow-lg rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-calendar-week mr-2 text-indigo-600"></i>
                Weekly Schedule
            </h3>
        </div>
        
        <?php if (!empty($schedule_by_day)): ?>
            <div class="p-6">
                <?php 
                $days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                foreach ($days_of_week as $day): 
                    if (isset($schedule_by_day[$day])): 
                ?>
                    <div class="mb-6 last:mb-0">
                        <h4 class="text-md font-semibold text-gray-800 mb-3 flex items-center">
                            <div class="w-3 h-3 bg-indigo-500 rounded-full mr-3"></div>
                            <?php echo $day; ?>
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <?php foreach ($schedule_by_day[$day] as $class): ?>
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition duration-200">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h5 class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($class['course_name']); ?></h5>
                                            <p class="text-xs text-gray-500 mb-2"><?php echo htmlspecialchars($class['course_code']); ?> - Section <?php echo htmlspecialchars($class['section']); ?></p>
                                            
                                            <div class="space-y-1 text-xs text-gray-600">
                                                <div class="flex items-center">
                                                    <i class="fas fa-clock w-4 mr-2"></i>
                                                    <?php echo date('g:i A', strtotime($class['start_time'])) . ' - ' . date('g:i A', strtotime($class['end_time'])); ?>
                                                </div>
                                                <div class="flex items-center">
                                                    <i class="fas fa-map-marker-alt w-4 mr-2"></i>
                                                    <?php echo htmlspecialchars($class['room_number'] ?: 'TBA'); ?>
                                                </div>
                                                <div class="flex items-center">
                                                    <i class="fas fa-user w-4 mr-2"></i>
                                                    <?php echo htmlspecialchars($class['teacher_name']); ?>
                                                </div>
                                                <div class="flex items-center">
                                                    <i class="fas fa-building w-4 mr-2"></i>
                                                    <?php echo htmlspecialchars($class['department_name']); ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="ml-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                                <?php echo $class['credits']; ?> Credits
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php 
                    endif;
                endforeach; 
                ?>
            </div>
        <?php else: ?>
            <div class="text-center py-8">
                <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">No classes scheduled. Please contact your academic advisor.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include '../includes/footer.php'; ?>

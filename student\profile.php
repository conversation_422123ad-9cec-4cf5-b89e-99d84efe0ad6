<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'My Profile';
$student_id = $_SESSION['user_id'];
$success_message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $full_name = trim($_POST['full_name']);
        $email = trim($_POST['email']);
        $phone = trim($_POST['phone']);
        $address = trim($_POST['address']);
        $emergency_contact = trim($_POST['emergency_contact']);
        $emergency_phone = trim($_POST['emergency_phone']);
        
        // Validate required fields
        if (empty($full_name) || empty($email)) {
            throw new Exception("Full name and email are required.");
        }
        
        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Please enter a valid email address.");
        }
        
        // Check if email is already taken by another user
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $stmt->execute([$email, $student_id]);
        if ($stmt->fetch()) {
            throw new Exception("This email address is already in use by another user.");
        }
        
        // Update user profile
        $stmt = $db->prepare("
            UPDATE users 
            SET full_name = ?, email = ?, phone = ?, address = ?, 
                emergency_contact = ?, emergency_phone = ?, updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$full_name, $email, $phone, $address, $emergency_contact, $emergency_phone, $student_id]);
        
        // Update session data
        $_SESSION['full_name'] = $full_name;
        $_SESSION['email'] = $email;
        
        $success_message = "Profile updated successfully!";
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get student profile data
try {
    $stmt = $db->prepare("
        SELECT 
            u.*,
            d.name as department_name,
            d.code as department_code
        FROM users u
        LEFT JOIN student_details sd ON u.id = sd.student_id
        LEFT JOIN departments d ON sd.department_id = d.id
        WHERE u.id = ?
    ");
    $stmt->execute([$student_id]);
    $student = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$student) {
        throw new Exception("Student profile not found.");
    }
    
    // Get enrollment statistics
    $stmt = $db->prepare("
        SELECT 
            COUNT(DISTINCT ce.class_id) as total_classes,
            SUM(co.credits) as total_credits,
            MIN(ce.enrolled_at) as first_enrollment
        FROM class_enrollments ce
        JOIN classes c ON ce.class_id = c.id
        JOIN courses co ON c.course_id = co.id
        WHERE ce.student_id = ?
    ");
    $stmt->execute([$student_id]);
    $enrollment_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
}

include '../includes/header.php';
?>

<div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-user-cog mr-3 text-indigo-600"></i>
                    My Profile
                </h1>
                <p class="mt-2 text-gray-600">Manage your personal information and account settings</p>
            </div>
            <a href="dashboard.php" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success_message): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <i class="fas fa-check-circle mr-2"></i>
            <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <i class="fas fa-exclamation-circle mr-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Summary -->
        <div class="lg:col-span-1">
            <div class="bg-white shadow-lg rounded-lg p-6">
                <div class="text-center">
                    <div class="h-20 w-20 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user text-indigo-600 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($student['full_name']); ?></h3>
                    <p class="text-sm text-gray-600 mb-2"><?php echo htmlspecialchars($student['student_id'] ?? 'N/A'); ?></p>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                        Student
                    </span>
                </div>
                
                <div class="mt-6 space-y-3">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Department:</span>
                        <span class="font-medium text-gray-900"><?php echo htmlspecialchars($student['department_name'] ?? 'Not assigned'); ?></span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Total Classes:</span>
                        <span class="font-medium text-gray-900"><?php echo $enrollment_stats['total_classes'] ?? 0; ?></span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Total Credits:</span>
                        <span class="font-medium text-gray-900"><?php echo $enrollment_stats['total_credits'] ?? 0; ?></span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Member Since:</span>
                        <span class="font-medium text-gray-900"><?php echo date('M Y', strtotime($student['created_at'])); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Form -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-edit mr-2 text-indigo-600"></i>
                        Edit Profile Information
                    </h3>
                </div>
                
                <form method="POST" class="p-6 space-y-6">
                    <!-- Personal Information -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-4">Personal Information</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="full_name" class="block text-sm font-medium text-gray-700 mb-1">
                                    Full Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="full_name" name="full_name" required
                                       value="<?php echo htmlspecialchars($student['full_name']); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>
                            
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                                    Email Address <span class="text-red-500">*</span>
                                </label>
                                <input type="email" id="email" name="email" required
                                       value="<?php echo htmlspecialchars($student['email']); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>
                            
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">
                                    Phone Number
                                </label>
                                <input type="tel" id="phone" name="phone"
                                       value="<?php echo htmlspecialchars($student['phone'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>
                            
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700 mb-1">
                                    Address
                                </label>
                                <input type="text" id="address" name="address"
                                       value="<?php echo htmlspecialchars($student['address'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>
                        </div>
                    </div>

                    <!-- Emergency Contact -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-4">Emergency Contact</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="emergency_contact" class="block text-sm font-medium text-gray-700 mb-1">
                                    Emergency Contact Name
                                </label>
                                <input type="text" id="emergency_contact" name="emergency_contact"
                                       value="<?php echo htmlspecialchars($student['emergency_contact'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>
                            
                            <div>
                                <label for="emergency_phone" class="block text-sm font-medium text-gray-700 mb-1">
                                    Emergency Contact Phone
                                </label>
                                <input type="tel" id="emergency_phone" name="emergency_phone"
                                       value="<?php echo htmlspecialchars($student['emergency_phone'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-save mr-2"></i>
                            Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
